// Test Supabase Connection for MyStation
import { supabase, testConnection, electricityAPI } from './supabase-client.js'

async function runTests() {
  console.log('🔌 Testing MyStation Supabase Connection...\n')
  
  // Test 1: Basic connection
  console.log('1️⃣ Testing basic connection...')
  const isConnected = await testConnection()
  
  if (!isConnected) {
    console.log('❌ Connection failed. Check if Supabase is running.')
    return
  }
  
  // Test 2: Database query
  console.log('\n2️⃣ Testing database query...')
  try {
    const { data: users, error } = await electricityAPI.users.getAll()
    if (error) {
      console.log('⚠️ Database query error:', error.message)
    } else {
      console.log(`✅ Users table accessible. Found ${users?.length || 0} users.`)
    }
  } catch (err) {
    console.log('❌ Database query failed:', err.message)
  }
  
  // Test 3: Storage buckets
  console.log('\n3️⃣ Testing storage buckets...')
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets()
    if (error) {
      console.log('⚠️ Storage error:', error.message)
    } else {
      console.log('✅ Storage accessible. Available buckets:')
      buckets.forEach(bucket => {
        console.log(`   📁 ${bucket.name} (${bucket.public ? 'public' : 'private'})`)
      })
    }
  } catch (err) {
    console.log('❌ Storage test failed:', err.message)
  }
  
  // Test 4: Authentication
  console.log('\n4️⃣ Testing authentication...')
  try {
    const { data: { session } } = await supabase.auth.getSession()
    console.log(`✅ Auth service accessible. Current session: ${session ? 'Active' : 'None'}`)
  } catch (err) {
    console.log('❌ Auth test failed:', err.message)
  }
  
  console.log('\n🎉 Connection tests completed!')
  console.log('\n📋 Your Supabase endpoints:')
  console.log(`   🌐 API: http://192.168.0.102:54321`)
  console.log(`   🎛️ Studio: http://192.168.0.102:54323`)
  console.log(`   🗄️ Database: **************************************************/postgres`)
}

// Run the tests
runTests().catch(console.error)
