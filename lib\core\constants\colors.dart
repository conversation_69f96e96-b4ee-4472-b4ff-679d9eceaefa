import 'package:flutter/material.dart';

class AppColors {
  // الألوان الأساسية
  static const Color primary = Color(0xFF1976D2);
  static const Color primaryDark = Color(0xFF1565C0);
  static const Color primaryLight = Color(0xFF42A5F5);
  
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryDark = Color(0xFFF57C00);
  static const Color secondaryLight = Color(0xFFFFB74D);
  
  // ألوان الحالة
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // ألوان محايدة
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color divider = Color(0xFFBDBDBD);
  
  // ألوان خاصة بالكهرباء
  static const Color electricity = Color(0xFFFFD700);
  static const Color power = Color(0xFFE91E63);
  static const Color voltage = Color(0xFF9C27B0);
  
  // ألوان الأدوار
  static const Color owner = Color(0xFF673AB7);
  static const Color manager = Color(0xFF3F51B5);
  static const Color collector = Color(0xFF009688);
  static const Color financial = Color(0xFF795548);
  static const Color admin = Color(0xFF607D8B);
  static const Color auditor = Color(0xFF795548);
}