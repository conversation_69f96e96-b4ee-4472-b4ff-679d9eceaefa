# 🚀 Electricity Management System - Quick Start Guide

## Prerequisites

- Flutter SDK (3.8.0 or higher)
- Supabase CLI installed
- Local Supabase instance running

## 🏃‍♂️ Quick Start

### 1. Install Dependencies

```bash
flutter pub get
```

### 2. Start Supabase Local Development

```bash
cd supabase
supabase start
```

This will start:
- **API**: http://127.0.0.1:54321
- **Studio**: http://127.0.0.1:54323
- **Database**: postgresql://postgres:postgres@127.0.0.1:54322/postgres

### 3. Apply Database Migrations

```bash
cd supabase
supabase db reset
```

This will:
- Create all database tables
- Set up Row Level Security policies
- Create storage buckets
- Insert sample data

### 4. Run the Flutter App

```bash
flutter run
```

## 🔧 Configuration

### Supabase Configuration

The app is configured to connect to your local Supabase instance:

- **URL**: http://127.0.0.1:54321
- **Anon Key**: Automatically configured from your local instance
- **Service Key**: Available for admin operations

### Storage Buckets

The system automatically creates 4 storage buckets:
- `documents` - General documents (10MB limit)
- `meter_images` - Meter photos (5MB limit)
- `reports` - Generated reports (25MB limit)
- `maintenance_photos` - Maintenance photos (10MB limit)

## 👤 User Management

### Default User Roles

- **Admin**: Full access to all data and functions
- **Manager**: Can manage stations, meters, billing
- **Operator**: Can create readings and maintenance logs
- **Viewer**: Read-only access to assigned stations

### Creating Your First Admin User

1. Run the app and sign up with your email
2. Connect to the database and update your role:

```sql
UPDATE public.profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

## 📱 App Features

### Current Implementation
- ✅ Supabase client initialization
- ✅ Authentication service
- ✅ Database service
- ✅ Storage service
- ✅ Basic UI structure
- ✅ Role-based access control

### Coming Soon
- 🔄 User authentication screens
- 🔄 Station management
- 🔄 Meter readings
- 🔄 Billing management
- 🔄 Maintenance logs
- 🔄 File upload/download
- 🔄 Reports and analytics

## 🗄️ Database Schema

The system includes 6 main tables:
- `profiles` - User profiles and roles
- `stations` - Power stations
- `meters` - Electricity meters
- `meter_readings` - Consumption readings
- `billing` - Billing records
- `maintenance_logs` - Maintenance activities

## 🔐 Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Role-based access control** for data and functions
- **Station-based data isolation** for multi-tenant support
- **Secure storage policies** for file access

## 🐛 Troubleshooting

### Common Issues

1. **Supabase not starting**
   ```bash
   supabase stop
   supabase start
   ```

2. **Database connection errors**
   - Check if Supabase is running: `supabase status`
   - Verify ports are not in use

3. **Migration errors**
   - Reset database: `supabase db reset`
   - Check migration files for syntax errors

4. **Flutter build errors**
   - Clean and rebuild: `flutter clean && flutter pub get`

### Useful Commands

```bash
# Check Supabase status
supabase status

# View logs
supabase logs

# Reset database
supabase db reset

# Open Studio
supabase studio

# Stop Supabase
supabase stop
```

## 📚 Next Steps

1. **Complete the authentication UI** - Add login/signup screens
2. **Implement station management** - CRUD operations for stations
3. **Add meter reading functionality** - Input and display readings
4. **Create billing system** - Generate and manage bills
5. **Build maintenance tracking** - Log and track maintenance activities
6. **Add file management** - Upload/download documents and images
7. **Create reports** - Analytics and reporting features

## 🆘 Support

- Check Supabase logs: `supabase logs`
- Review the README.md in the supabase directory
- Check Flutter console for error messages
- Verify all services are running: `supabase status`

## 🌟 Features Overview

Your electricity management system now includes:

- **Complete database schema** with 6 core tables
- **User authentication** with role-based access
- **Secure storage** with 4 specialized buckets
- **Row-level security** for data protection
- **Sample data** to get started quickly
- **Flutter integration** ready for UI development

You're all set to start building the electricity management system! 🎉
Started supabase local development setup.

         API URL: http://127.0.0.1:54321
     GraphQL URL: http://127.0.0.1:54321/graphql/v1
  S3 Storage URL: http://127.0.0.1:54321/storage/v1/s3
          DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
      Studio URL: http://127.0.0.1:54323
    Inbucket URL: http://127.0.0.1:54324
      JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
        anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
   S3 Access Key: 625729a08b95bf1b7ff351a663f3a23c
   S3 Secret Key: 850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
       S3 Region: local