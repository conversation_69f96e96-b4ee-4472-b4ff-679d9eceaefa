{"name": "supabase", "version": "2.34.3", "description": "Supabase CLI", "repository": "supabase/cli", "homepage": "https://supabase.com/docs/reference/cli", "bugs": "https://github.com/supabase/cli/issues", "license": "MIT", "author": "Supabase", "type": "module", "engines": {"npm": ">=8"}, "files": ["scripts"], "scripts": {"postinstall": "node scripts/postinstall.js"}, "bin": {"supabase": "bin/supabase"}, "dependencies": {"bin-links": "^5.0.0", "https-proxy-agent": "^7.0.2", "node-fetch": "^3.3.2", "tar": "7.4.3"}, "release": {"branches": [{"name": "+([0-9])?(.{+([0-9]),x}).x", "channel": "hotfix"}, {"name": "develop", "channel": "beta"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/git"]}}