// Supabase Client Configuration for MyStation Electricity Management System
import { createClient } from '@supabase/supabase-js'

// Configuration for different environments
const config = {
  // Local development (your machine only)
  local: {
    url: 'http://127.0.0.1:54321',
    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
  },
  
  // Local network (other devices on your Wi-Fi)
  network: {
    url: 'http://192.168.0.102:54321',
    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
  }
}

// Determine which environment to use
const environment = process.env.NODE_ENV === 'production' ? 'network' : 'local'
const supabaseUrl = config[environment].url
const supabaseKey = config[environment].key

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Service role key for admin operations (use carefully!)
const supabaseAdmin = createClient(supabaseUrl, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU', {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Helper functions for your electricity management system
export const electricityAPI = {
  // Users management
  users: {
    async getAll() {
      const { data, error } = await supabase
        .from('users')
        .select('*')
      return { data, error }
    },
    
    async create(userData) {
      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
      return { data, error }
    },
    
    async update(id, updates) {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', id)
        .select()
      return { data, error }
    }
  },

  // Storage for documents, meter images, etc.
  storage: {
    async uploadMeterImage(file, fileName) {
      const { data, error } = await supabase.storage
        .from('meter_images')
        .upload(fileName, file)
      return { data, error }
    },
    
    async uploadDocument(file, fileName) {
      const { data, error } = await supabase.storage
        .from('documents')
        .upload(fileName, file)
      return { data, error }
    },
    
    async uploadMaintenancePhoto(file, fileName) {
      const { data, error } = await supabase.storage
        .from('maintenance_photos')
        .upload(fileName, file)
      return { data, error }
    },
    
    async uploadReport(file, fileName) {
      const { data, error } = await supabase.storage
        .from('reports')
        .upload(fileName, file)
      return { data, error }
    }
  },

  // Authentication
  auth: {
    async signUp(email, password, userData) {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })
      return { data, error }
    },
    
    async signIn(email, password) {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      return { data, error }
    },
    
    async signOut() {
      const { error } = await supabase.auth.signOut()
      return { error }
    },
    
    async getCurrentUser() {
      const { data: { user } } = await supabase.auth.getUser()
      return user
    }
  },

  // Real-time subscriptions
  realtime: {
    subscribeToUsers(callback) {
      return supabase
        .channel('users_changes')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'users' }, 
          callback
        )
        .subscribe()
    }
  }
}

// Export the main client and admin client
export { supabase, supabaseAdmin }
export default supabase

// Connection test function
export async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true })
    
    if (error) {
      console.error('Connection test failed:', error)
      return false
    }
    
    console.log('✅ Supabase connection successful!')
    console.log(`📊 Connected to: ${supabaseUrl}`)
    return true
  } catch (err) {
    console.error('❌ Connection test error:', err)
    return false
  }
}
