class AppRoles {
  // معرفات الأدوار
  static const int owner = 1;
  static const int manager = 2;
  static const int collector = 3;
  static const int financial = 4;
  static const int admin = 5;
  static const int auditor = 6;
  
  // أسماء الأدوار بالعربية (كما ستكون في قاعدة البيانات)
  static const String ownerName = 'مالك التطبيق';
  static const String managerName = 'مدير المحطة';
  static const String collectorName = 'محصل';
  static const String financialName = 'مسؤول مالي';
  static const String adminName = 'مسؤول النظام';
  static const String auditorName = 'مراجع';
  
  // أسماء الأدوار بالإنجليزية (للاستخدام الداخلي)
  static const String ownerNameEn = 'owner';
  static const String managerNameEn = 'manager';
  static const String collectorNameEn = 'collector';
  static const String financialNameEn = 'financial';
  static const String adminNameEn = 'admin';
  static const String auditorNameEn = 'auditor';
  
  // فئات الأدوار بالعربية
  static const String categoryOwner = 'نظام';
  static const String categoryStation = 'محطة';
  static const String categoryFinancial = 'مالي';
  static const String categoryAudit = 'مراجعة';
  
  // وصف الأدوار بالعربية
  static const String ownerDescription = 'يملك التطبيق وله صلاحيات كاملة على النظام';
  static const String managerDescription = 'يدير محطة كهربائية محددة وموظفيها';
  static const String collectorDescription = 'يجمع المدفوعات والفواتير من العملاء';
  static const String financialDescription = 'يتابع الأمور المالية والمحاسبية';
  static const String adminDescription = 'يدير المستخدمين والإعدادات في المحطة';
  static const String auditorDescription = 'يراجع الحسابات والتقارير للتأكد من صحتها';
  
  // الصلاحيات بالعربية
  static const Map<String, String> permissionsArabic = {
    'manage_subscription_plans': 'إدارة خطط الاشتراك',
    'manage_stations': 'إدارة المحطات',
    'manage_users': 'إدارة المستخدمين',
    'view_reports': 'عرض التقارير',
    'system_settings': 'إعدادات النظام',
    'manage_station_users': 'إدارة مستخدمي المحطة',
    'manage_substations': 'إدارة الفروع',
    'view_station_reports': 'عرض تقارير المحطة',
    'manage_station_settings': 'إعدادات المحطة',
    'record_payments': 'تسجيل المدفوعات',
    'view_bills': 'عرض الفواتير',
    'view_payment_history': 'عرض سجل المدفوعات',
    'view_financial_reports': 'عرض التقارير المالية',
    'manage_bills': 'إدارة الفواتير',
    'view_payment_reports': 'عرض تقارير المدفوعات',
    'view_logs': 'عرض السجلات',
    'view_audit_reports': 'عرض تقارير المراجعة',
    'view_readings': 'عرض القراءات',
    'audit_financial_records': 'مراجعة السجلات المالية',
  };
  
  // الحصول على اسم الدور بالعربية
  static String getRoleNameArabic(int roleId) {
    switch (roleId) {
      case owner:
        return ownerName;
      case manager:
        return managerName;
      case collector:
        return collectorName;
      case financial:
        return financialName;
      case admin:
        return adminName;
      case auditor:
        return auditorName;
      default:
        return 'غير محدد';
    }
  }
  
  // الحصول على اسم الدور بالإنجليزية
  static String getRoleNameEnglish(int roleId) {
    switch (roleId) {
      case owner:
        return ownerNameEn;
      case manager:
        return managerNameEn;
      case collector:
        return collectorNameEn;
      case financial:
        return financialNameEn;
      case admin:
        return adminNameEn;
      case auditor:
        return auditorNameEn;
      default:
        return 'unknown';
    }
  }
  
  // الحصول على وصف الدور
  static String getRoleDescription(int roleId) {
    switch (roleId) {
      case owner:
        return ownerDescription;
      case manager:
        return managerDescription;
      case collector:
        return collectorDescription;
      case financial:
        return financialDescription;
      case admin:
        return adminDescription;
      case auditor:
        return auditorDescription;
      default:
        return 'دور غير محدد';
    }
  }
  
  // الحصول على فئة الدور
  static String getRoleCategory(int roleId) {
    switch (roleId) {
      case owner:
        return categoryOwner;
      case manager:
      case admin:
        return categoryStation;
      case collector:
      case financial:
        return categoryFinancial;
      case auditor:
        return categoryAudit;
      default:
        return 'غير محدد';
    }
  }
  
  // الصلاحيات حسب الدور
  static const List<String> ownerPermissions = [
    'manage_subscription_plans',
    'manage_stations',
    'manage_users',
    'view_reports',
    'system_settings'
  ];
  
  static const List<String> managerPermissions = [
    'manage_station_users',
    'manage_substations',
    'view_station_reports',
    'manage_station_settings'
  ];
  
  static const List<String> collectorPermissions = [
    'record_payments',
    'view_bills',
    'view_payment_history'
  ];
  
  static const List<String> financialPermissions = [
    'view_financial_reports',
    'manage_bills',
    'view_payment_reports'
  ];
  
  static const List<String> adminPermissions = [
    'manage_station_users',
    'system_settings',
    'view_logs'
  ];
  
  static const List<String> auditorPermissions = [
    'view_audit_reports',
    'view_readings',
    'audit_financial_records'
  ];
  
  // الحصول على صلاحيات الدور
  static List<String> getRolePermissions(int roleId) {
    switch (roleId) {
      case owner:
        return ownerPermissions;
      case manager:
        return managerPermissions;
      case collector:
        return collectorPermissions;
      case financial:
        return financialPermissions;
      case admin:
        return adminPermissions;
      case auditor:
        return auditorPermissions;
      default:
        return [];
    }
  }
  
  // الحصول على صلاحيات الدور بالعربية
  static List<String> getRolePermissionsArabic(int roleId) {
    final permissions = getRolePermissions(roleId);
    return permissions.map((permission) => 
      permissionsArabic[permission] ?? permission
    ).toList();
  }
  
  // التحقق من الصلاحية
  static bool hasPermission(int roleId, String permission) {
    final permissions = getRolePermissions(roleId);
    return permissions.contains(permission);
  }
  
  // الحصول على جميع الأدوار مع معلوماتها
  static List<Map<String, dynamic>> getAllRolesInfo() {
    return [
      {
        'id': owner,
        'name_arabic': ownerName,
        'name_english': ownerNameEn,
        'category': categoryOwner,
        'description': ownerDescription,
        'permissions': ownerPermissions,
      },
      {
        'id': manager,
        'name_arabic': managerName,
        'name_english': managerNameEn,
        'category': categoryStation,
        'description': managerDescription,
        'permissions': managerPermissions,
      },
      {
        'id': collector,
        'name_arabic': collectorName,
        'name_english': collectorNameEn,
        'category': categoryFinancial,
        'description': collectorDescription,
        'permissions': collectorPermissions,
      },
      {
        'id': financial,
        'name_arabic': financialName,
        'name_english': financialNameEn,
        'category': categoryFinancial,
        'description': financialDescription,
        'permissions': financialPermissions,
      },
      {
        'id': admin,
        'name_arabic': adminName,
        'name_english': adminNameEn,
        'category': categoryStation,
        'description': adminDescription,
        'permissions': adminPermissions,
      },
      {
        'id': auditor,
        'name_arabic': auditorName,
        'name_english': auditorNameEn,
        'category': categoryAudit,
        'description': auditorDescription,
        'permissions': auditorPermissions,
      },
    ];
  }
  
  // البحث عن دور بالاسم العربي
  static int? findRoleIdByNameArabic(String nameArabic) {
    final roles = getAllRolesInfo();
    final role = roles.firstWhere(
      (role) => role['name_arabic'] == nameArabic,
      orElse: () => {'id': null},
    );
    return role['id'];
  }
  
  // البحث عن دور بالاسم الإنجليزي
  static int? findRoleIdByNameEnglish(String nameEnglish) {
    final roles = getAllRolesInfo();
    final role = roles.firstWhere(
      (role) => role['name_english'] == nameEnglish,
      orElse: () => {'id': null},
    );
    return role['id'];
  }
  
  // الحصول على الأدوار حسب الفئة
  static List<Map<String, dynamic>> getRolesByCategory(String category) {
    final roles = getAllRolesInfo();
    return roles.where((role) => role['category'] == category).toList();
  }
  
  // التحقق من أن الدور صالح
  static bool isValidRole(int roleId) {
    return roleId >= 1 && roleId <= 6;
  }
  
  // الحصول على مستوى الصلاحيات (1 = أعلى، 6 = أدنى)
  static int getRoleLevel(int roleId) {
    switch (roleId) {
      case owner:
        return 1;
      case manager:
        return 2;
      case admin:
        return 3;
      case financial:
        return 4;
      case collector:
        return 5;
      case auditor:
        return 6;
      default:
        return 99;
    }
  }
  
  // التحقق من أن دور له صلاحيات أعلى من دور آخر
  static bool hasHigherRole(int roleId1, int roleId2) {
    return getRoleLevel(roleId1) < getRoleLevel(roleId2);
  }
}